const mongoose = require('mongoose')

console.log('=== MESSAGE MODEL LOADED ===')

const MessageSchema = new mongoose.Schema({
  ticket: {
    type: mongoose.Schema.ObjectId,
    ref: 'Ticket',
    required: true
  },
  sender: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  content: {
    type: String,
    required: [true, 'Please add message content'],
    maxlength: [2000, 'Message cannot be more than 2000 characters'],
    validate: {
      validator: function(value) {
        console.log('=== CONTENT VALIDATION CALLED ===')
        console.log('Value being validated:', value)
        console.log('Type of value:', typeof value)
        console.log('Is undefined?', value === undefined)
        console.log('Is null?', value === null)
        console.log('Is empty string?', value === '')
        console.log('Stack trace from validation:')
        console.trace()
        return true // Always return true to avoid double validation error
      },
      message: 'Custom validation failed'
    }
  },
  messageType: {
    type: String,
    enum: ['message', 'system', 'note'],
    default: 'message'
  },
  isInternal: {
    type: Boolean,
    default: false // Internal notes only visible to agents
  },
  readBy: [{
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    readAt: {
      type: Date,
      default: Date.now
    }
  }],
  editedAt: {
    type: Date,
    default: null
  },
  editedBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    default: null
  },
  originalContent: {
    type: String,
    default: null
  },
  metadata: {
    userAgent: String,
    ipAddress: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Virtual for attachments
MessageSchema.virtual('attachments', {
  ref: 'Attachment',
  localField: '_id',
  foreignField: 'message',
  justOne: false
})

// Debug logging for message creation
MessageSchema.pre('save', function(next) {
  console.log('=== MESSAGE PRE-SAVE HOOK CALLED ===')
  console.log('Content value:', this.content)
  console.log('Content type:', typeof this.content)
  console.log('Is content undefined?', this.content === undefined)
  console.log('Is content null?', this.content === null)
  console.log('Is content empty string?', this.content === '')
  console.log('Full message object:', JSON.stringify(this.toObject(), null, 2))
  console.trace('Stack trace from pre-save hook:')
  next()
})

// Update ticket's lastActivity when message is created
MessageSchema.post('save', async function() {
  try {
    await this.model('Ticket').findByIdAndUpdate(this.ticket, {
      lastActivity: Date.now()
    })
  } catch (error) {
    console.error('Error updating ticket lastActivity:', error)
  }
})

// Index for better performance
MessageSchema.index({ ticket: 1, createdAt: 1 })
MessageSchema.index({ sender: 1, createdAt: -1 })

module.exports = mongoose.model('Message', MessageSchema)
