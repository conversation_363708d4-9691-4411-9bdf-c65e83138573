const mongoose = require('mongoose')

const MessageSchema = new mongoose.Schema({
  ticket: {
    type: mongoose.Schema.ObjectId,
    ref: 'Ticket',
    required: true
  },
  sender: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  content: {
    type: String,
    required: [true, 'Please add message content'],
    maxlength: [2000, 'Message cannot be more than 2000 characters']
  },
  messageType: {
    type: String,
    enum: ['message', 'system', 'note'],
    default: 'message'
  },
  isInternal: {
    type: Boolean,
    default: false // Internal notes only visible to agents
  },
  readBy: [{
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    readAt: {
      type: Date,
      default: Date.now
    }
  }],
  editedAt: {
    type: Date,
    default: null
  },
  editedBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    default: null
  },
  originalContent: {
    type: String,
    default: null
  },
  metadata: {
    userAgent: String,
    ipAddress: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Virtual for attachments
MessageSchema.virtual('attachments', {
  ref: 'Attachment',
  localField: '_id',
  foreignField: 'message',
  justOne: false
})

// Debug logging for message creation
MessageSchema.pre('save', function(next) {
  console.log('=== MESSAGE PRE-SAVE HOOK ===')
  console.log('Message data:', {
    ticket: this.ticket,
    sender: this.sender,
    content: this.content,
    messageType: this.messageType,
    isInternal: this.isInternal
  })
  console.log('Stack trace:')
  console.trace()
  next()
})

// Update ticket's lastActivity when message is created
MessageSchema.post('save', async function() {
  try {
    await this.model('Ticket').findByIdAndUpdate(this.ticket, {
      lastActivity: Date.now()
    })
  } catch (error) {
    console.error('Error updating ticket lastActivity:', error)
  }
})

// Index for better performance
MessageSchema.index({ ticket: 1, createdAt: 1 })
MessageSchema.index({ sender: 1, createdAt: -1 })

module.exports = mongoose.model('Message', MessageSchema)
