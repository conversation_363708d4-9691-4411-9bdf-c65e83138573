const Message = require('../models/message')
const Ticket = require('../models/ticket')
const User = require('../models/user')
const ErrorResponse = require('../utils/errorResponse')
const asyncHandler = require('../middleware/async')
const { Socket, io } = require('../utils/socket')
const { sendNewMessageNotification } = require('../utils/emailNotifications')

// @desc    Get messages for a ticket
// @route   GET /api/v1/tickets/:ticketId/messages
// @access  Private
exports.getMessages = asyncHandler(async (req, res, next) => {
  // Determine if this is a socket request or HTTP request
  const isSocketRequest = !res || typeof res.status !== 'function'

  // Get ticket ID from either req.params.ticketId (HTTP) or req.params?.ticketId (Socket)
  const ticketId = req.params?.ticketId || req.ticketId

  // Get user from either req.user (HTTP) or directly from socket auth
  const user = req.user
  const userId = user?.id || user
  const userRole = user?.role || 'user'

  const ticket = await Ticket.findById(ticketId)

  if (!ticket) {
    if (isSocketRequest) {
      return io.to(req.user).emit('messageController:getMessages', {
        status: 'error',
        data: {
          message: 'Ticket not found',
        },
      })
    } else {
      return res.status(404).json({
        success: false,
        error: 'Ticket not found'
      })
    }
  }

  // Check permissions
  if (userRole === 'user' && ticket.user.toString() !== userId) {
    if (isSocketRequest) {
      return io.to(req.user).emit('messageController:getMessages', {
        status: 'error',
        data: {
          message: 'Not authorized to access these messages',
        },
      })
    } else {
      return res.status(403).json({
        success: false,
        error: 'Not authorized to access these messages'
      })
    }
  }

  let query = { ticket: ticketId }

  // Users cannot see internal notes
  if (userRole === 'user') {
    query.isInternal = false
  }

  const messages = await Message.find(query)
    .populate('sender', 'name email role')
    .populate('attachments')
    .sort('createdAt')

  // Send response based on request type
  if (isSocketRequest) {
    io.to(req.user).emit('messageController:getMessages', {
      status: 'success',
      data: {
        count: messages.length,
        messages: messages,
      },
    })
  } else {
    res.status(200).json({
      success: true,
      count: messages.length,
      data: messages
    })
  }
})

// @desc    Create new message
// @route   POST /api/v1/tickets/:ticketId/messages
// @access  Private
exports.createMessage = asyncHandler(async (req, res, next) => {
  console.log('=== createMessage CONTROLLER function called ===')
  console.log('req.params:', req.params)
  console.log('req.body:', req.body)
  console.log('req.user:', req.user)

  // Determine if this is a socket request or HTTP request
  const isSocketRequest = !res || typeof res.status !== 'function'
  console.log('isSocketRequest:', isSocketRequest)

  // Get ticket ID from either req.params.ticketId (HTTP) or req.params?.ticketId (Socket)
  const ticketId = req.params?.ticketId || req.ticketId
  console.log('ticketId:', ticketId)

  // Get user from either req.user (HTTP) or directly from socket auth
  const user = req.user
  const userId = user?.id || user
  const userRole = user?.role || 'user'
  console.log('userId:', userId, 'userRole:', userRole)

  const ticket = await Ticket.findById(ticketId)

  if (!ticket) {
    if (isSocketRequest) {
      return io.to(req.user).emit('messageController:createMessage', {
        status: 'error',
        data: {
          message: 'Ticket not found',
        },
      })
    } else {
      return res.status(404).json({
        success: false,
        error: 'Ticket not found'
      })
    }
  }

  // Check permissions
  if (userRole === 'user' && ticket.user.toString() !== userId) {
    if (isSocketRequest) {
      return io.to(req.user).emit('messageController:createMessage', {
        status: 'error',
        data: {
          message: 'Not authorized to message this ticket',
        },
      })
    } else {
      return res.status(403).json({
        success: false,
        error: 'Not authorized to message this ticket'
      })
    }
  }

  // Users cannot create internal notes
  if (userRole === 'user' && req.body.isInternal) {
    req.body.isInternal = false
  }

  // Create message data object
  const messageData = {
    ...req.body,
    ticket: ticketId,
    sender: userId,
  }

  let message
  try {
    console.log('=== ABOUT TO CREATE MESSAGE IN createMessage CONTROLLER ===')
    console.log('messageData:', JSON.stringify(messageData, null, 2))
    console.log('Stack trace:')
    console.trace()
    message = await Message.create(messageData)
    console.log('=== MESSAGE CREATED SUCCESSFULLY ===')
  } catch (error) {
    console.log('=== MESSAGE CREATION ERROR IN createMessage CONTROLLER ===')
    console.log('Message creation error:', error)
    if (isSocketRequest) {
      return io.to(req.user).emit('messageController:createMessage', {
        status: 'error',
        data: {
          message: error.message || 'Failed to create message',
          errors: error.errors || {},
        },
      })
    } else {
      return res.status(400).json({
        success: false,
        error: error.message || 'Failed to create message',
        errors: error.errors || {}
      })
    }
  }

  // Populate the created message
  await message.populate('sender', 'name email role')
  await message.populate('attachments')

  // Get ticket with populated user and assigned agent for email notifications
  const populatedTicket = await Ticket.findById(ticketId)
    .populate('user', 'name email role')
    .populate('assignedAgent', 'name email role')

  // Send email notifications to relevant parties
  const recipients = []

  // Add ticket owner if not the sender
  if (populatedTicket.user._id.toString() !== userId) {
    recipients.push(populatedTicket.user)
  }

  // Add assigned agent if not the sender and message is not internal
  if (populatedTicket.assignedAgent &&
      populatedTicket.assignedAgent._id.toString() !== userId &&
      !message.isInternal) {
    recipients.push(populatedTicket.assignedAgent)
  }

  // Send email notifications
  recipients.forEach(recipient => {
    sendNewMessageNotification(populatedTicket, recipient, message, user)
  })

  // Update ticket status if it was closed/resolved and user is responding
  if (['closed', 'resolved'].includes(ticket.status) && userRole === 'user') {
    await Ticket.findByIdAndUpdate(ticketId, {
      status: 'open',
    })
  }

  // Emit socket event for new message
  Socket.emitToTicket(ticketId, 'message:created', {
    message,
    ticketId: ticketId,
    sender: user,
  })

  // Send response based on request type
  if (isSocketRequest) {
    io.to(req.user).emit('messageController:createMessage', {
      status: 'success',
      data: message,
    })
  } else {
    res.status(201).json({
      success: true,
      data: message
    })
  }
})

// @desc    Update message
// @route   PUT /api/v1/messages/:id
// @access  Private
exports.updateMessage = asyncHandler(async (req, res, next) => {
  // Determine if this is a socket request or HTTP request
  const isSocketRequest = !res || typeof res.status !== 'function'

  // Get message ID from either req.params.id (HTTP) or req.params?.id (Socket)
  const messageId = req.params?.id || req.id

  // Get user from either req.user (HTTP) or directly from socket auth
  const user = req.user
  const userId = user?.id || user

  let message = await Message.findById(messageId)

  if (!message) {
    if (isSocketRequest) {
      return io.to(req.user).emit('messageController:updateMessage', {
        status: 'error',
        data: {
          message: 'Message not found',
        },
      })
    } else {
      return res.status(404).json({
        success: false,
        error: 'Message not found'
      })
    }
  }

  // Only sender can edit their own messages (within 15 minutes)
  if (message.sender.toString() !== userId) {
    io.to(req.user).emit('messageController:updateMessage', {
      status: 'error',
      data: {
        message: 'Not authorized to edit this message',
      },
    })
  }

  // Check if message is too old to edit (15 minutes)
  const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000)
  if (message.createdAt < fifteenMinutesAgo) {
    io.to(req.user).emit('messageController:updateMessage', {
      status: 'error',
      data: {
        message: 'Message is too old to edit',
      },
    })
  }

  // Store original content if this is the first edit
  const updateData = { ...req.body }
  if (!message.originalContent) {
    updateData.originalContent = message.content
  }

  updateData.editedAt = Date.now()
  updateData.editedBy = userId

  try {
    message = await Message.findByIdAndUpdate(messageId, updateData, {
      new: true,
      runValidators: true,
    }).populate('sender', 'name email role')
      .populate('attachments')
  } catch (error) {
    console.log('Message update error:', error)
    if (isSocketRequest) {
      return io.to(req.user).emit('messageController:updateMessage', {
        status: 'error',
        data: {
          message: error.message || 'Failed to update message',
          errors: error.errors || {},
        },
      })
    } else {
      return res.status(400).json({
        success: false,
        error: error.message || 'Failed to update message',
        errors: error.errors || {}
      })
    }
  }

  // Emit socket event for message update
  Socket.emitToTicket(message.ticket, 'message:updated', {
    message,
    editedBy: user,
  })

  // Send response based on request type
  if (isSocketRequest) {
    io.to(req.user).emit('messageController:updateMessage', {
      status: 'success',
      data: message,
    })
  } else {
    res.status(200).json({
      success: true,
      data: message
    })
  }
})

// @desc    Delete message
// @route   DELETE /api/v1/messages/:id
// @access  Private
exports.deleteMessage = asyncHandler(async (req, res, next) => {
  const message = await Message.findById(req.params.id)

  if (!message) {
    return next(new ErrorResponse('Message not found', 404))
  }

  // Only sender or admin can delete messages
  if (message.sender.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(new ErrorResponse('Not authorized to delete this message', 403))
  }

  await message.deleteOne()

  // Emit socket event for message deletion
  io.to(req.user).emit(message.ticket, 'message:deleted', {
    messageId: req.params.id,
    ticketId: message.ticket,
    deletedBy: req.user,
  })
})

// @desc    Mark message as read
// @route   PUT /api/v1/messages/:id/read
// @access  Private
exports.markAsRead = asyncHandler(async (req, res, next) => {
  // Get message ID from either req.params.id (HTTP) or req.params?.id (Socket)
  const messageId = req.params?.id || req.id

  // Get user from either req.user (HTTP) or directly from socket auth
  const user = req.user
  const userId = user?.id || user

  const message = await Message.findById(messageId)

  if (!message) {
    io.to(req.user).emit('messageController:markAsRead', {
      status: 'error',
      data: {
        message: 'Message not found',
      },
    })
  }

  // Check if user already marked as read
  const alreadyRead = message.readBy.some(
    read => read.user.toString() === userId,
  )

  if (!alreadyRead) {
    message.readBy.push({
      user: userId,
      readAt: Date.now(),
    })
    await message.save()
  }

  io.to(req.user).emit('messageController:markAsRead', {
    status: 'success',
    data: message,
  })
})

// @desc    Get unread message count for user
// @route   GET /api/v1/messages/unread-count
// @access  Private
exports.getUnreadCount = asyncHandler(async (req, res, next) => {
  // Get user from either req.user (HTTP) or directly from socket auth
  const user = req.user
  const userId = user?.id || user
  const userRole = user?.role || 'user'

  let ticketQuery = {}

  // Filter tickets based on user role
  if (userRole === 'user') {
    ticketQuery.user = userId
  } else if (userRole === 'agent') {
    ticketQuery.$or = [
      { assignedAgent: userId },
      { assignedAgent: null },
    ]
  }

  // Get tickets user has access to
  const tickets = await Ticket.find(ticketQuery).select('_id')
  const ticketIds = tickets.map(ticket => ticket._id)

  // Count unread messages
  const unreadCount = await Message.countDocuments({
    ticket: { $in: ticketIds },
    sender: { $ne: userId }, // Not sent by current user
    'readBy.user': { $ne: userId }, // Not read by current user
    isInternal: userRole === 'user' ? false : { $in: [true, false] },
  })

  io.to(req.user).emit('messageController:getUnreadCount', {
    status: 'success',
    data: { unreadCount },
  })
})
