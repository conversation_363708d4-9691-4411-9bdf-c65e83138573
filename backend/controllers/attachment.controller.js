const Attachment = require('../models/attachment')
const Ticket = require('../models/ticket')
const Message = require('../models/message')
const ErrorResponse = require('../utils/errorResponse')
const asyncHandler = require('../middleware/async')
const path = require('path')
const fs = require('fs')
const { Socket, io } = require('../utils/socket')

// @desc    Upload file attachment
// @route   POST /api/v1/attachments/upload
// @access  Private
exports.uploadAttachment = asyncHandler(async (req, res, next) => {
  if (!req.files || !req.files.file) {
    return next(new ErrorResponse('Please upload a file', 400))
  }

  const file = req.files.file
  const { ticketId, messageId } = req

  // Validate that attachment belongs to either ticket or message
  if (!ticketId && !messageId) {
    return next(new ErrorResponse('Attachment must belong to either a ticket or message', 400))
  }

  // Validate ticket/message exists and user has access
  if (ticketId) {
    const ticket = await Ticket.findById(ticketId)
    if (!ticket) {
      return next(new ErrorResponse('Ticket not found', 404))
    }
    
    // Check permissions
    if (req.user.role === 'user' && ticket.user.toString() !== req.user.id) {
      return next(new ErrorResponse('Not authorized to upload to this ticket', 403))
    }
  }

  if (messageId) {
    const message = await Message.findById(messageId)
    if (!message) {
      return next(new ErrorResponse('Message not found', 404))
    }
    
    // Check permissions
    if (message.sender.toString() !== req.user.id && req.user.role !== 'admin') {
      return next(new ErrorResponse('Not authorized to upload to this message', 403))
    }
  }

  // Check file size (max 10MB)
  if (file.size > process.env.MAX_FILE_UPLOAD || file.size > 10000000) {
    return next(new ErrorResponse('File size cannot exceed 10MB', 400))
  }

  // Check file type (allow common file types)
  const allowedTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/csv',
    'application/zip',
    'application/x-zip-compressed',
  ]

  if (!allowedTypes.includes(file.mimetype)) {
    return next(new ErrorResponse('File type not supported', 400))
  }

  // Create unique filename
  const timestamp = Date.now()
  const randomString = Math.random().toString(36).substring(2, 15)
  const fileExtension = path.extname(file.name)
  const filename = `${timestamp}_${randomString}${fileExtension}`

  // Create upload directory if it doesn't exist
  const uploadPath = path.join(process.env.FILE_UPLOAD_PATH || './public/uploads', 'attachments')
  if (!fs.existsSync(uploadPath)) {
    fs.mkdirSync(uploadPath, { recursive: true })
  }

  const filePath = path.join(uploadPath, filename)

  // Move file to upload directory
  file.mv(filePath, async err => {
    if (err) {
      console.error(err)
      
      return next(new ErrorResponse('Problem with file upload', 500))
    }

    try {
      // Create attachment record
      const attachment = await Attachment.create({
        filename,
        originalName: file.name,
        mimeType: file.mimetype,
        size: file.size,
        path: filePath,
        url: `/uploads/attachments/${filename}`,
        ticket: ticketId || null,
        message: messageId || null,
        uploadedBy: req.user.id,
      })

      await attachment.populate('uploadedBy', 'name email')

      // Emit socket event
      if (ticketId) {
        io.to(req.user).emit(ticketId, 'attachment:uploaded', {
          attachment,
          uploadedBy: req.user,
        })
      }
    } catch (error) {
      // Delete file if database save fails
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath)
      }

      io.to(req.user).emit(ticketId, 'attachment:uploaded', {
        status: 'error',
        message: 'Failed to save attachment',
      })
    }
  })
})

// @desc    Get attachments for ticket or message
// @route   GET /api/v1/attachments
// @access  Private
exports.getAttachments = asyncHandler(async (req, res, next) => {
  const { ticketId, messageId } = req.query
  let query = {}

  if (ticketId) {
    query.ticket = ticketId
    
    // Check ticket access
    const ticket = await Ticket.findById(ticketId)
    if (!ticket) {
      io.to(req.user).emit(ticketId, 'attachment:getAttachments', {
        status: 'error',
        message: 'Ticket not found',
      })
    }
    
    if (req.user.role === 'user' && ticket.user.toString() !== req.user.id) {
      io.to(req.user).emit(ticketId, 'attachment:getAttachments', {
        status: 'error',
        message: 'Not authorized to access these attachments',
      })
    }
  }

  if (messageId) {
    query.message = messageId
  }

  const attachments = await Attachment.find(query)
    .populate('uploadedBy', 'name email')
    .sort('-createdAt')

  io.to(req.user).emit(ticketId, 'attachment:getAttachments', {
    status: 'success',
    count: attachments.length,
    data: attachments,
  })
})

// @desc    Download attachment
// @route   GET /api/v1/attachments/:id/download
// @access  Private
exports.downloadAttachment = asyncHandler(async (req, res, next) => {
  const attachment = await Attachment.findById(req.params.id)

  if (!attachment) {
    io.to(req.user).emit('attachment:downloadAttachment', {
      status: 'error',
      message: 'Attachment not found',
    })
  }

  // Check permissions
  if (attachment.ticket) {
    const ticket = await Ticket.findById(attachment.ticket)
    if (req.user.role === 'user' && ticket.user.toString() !== req.user.id) {
      io.to(req.user).emit('attachment:downloadAttachment', {
        status: 'error',
        message: 'Not authorized to download this attachment',
      })
    }
  }

  // Check if file exists
  if (!fs.existsSync(attachment.path)) {
    io.to(req.user).emit('attachment:downloadAttachment', {
      status: 'error',
      message: 'File not found on server',
    })
  }

  // Increment download count
  attachment.downloadCount += 1
  await attachment.save()

  // Set headers for download
  res.setHeader('Content-Disposition', `attachment; filename="${attachment.originalName}"`)
  res.setHeader('Content-Type', attachment.mimeType)

  // Stream file
  const fileStream = fs.createReadStream(attachment.path)

  fileStream.pipe(res)
})

// @desc    Delete attachment
// @route   DELETE /api/v1/attachments/:id
// @access  Private
exports.deleteAttachment = asyncHandler(async (req, res, next) => {
  const attachment = await Attachment.findById(req.params.id)

  if (!attachment) {
    io.to(req.user).emit('attachment:delteAttachment', {
      status: 'error',
      message: 'Attachment not found',
    })
  }

  // Check permissions - only uploader or admin can delete
  if (attachment.uploadedBy.toString() !== req.user.id && req.user.role !== 'admin') {
    io.to(req.user).emit('attachment:delteAttachment', {
      status: 'error',
      message: 'Not authorized to delete this attachment',
    })
  }

  // Delete file from filesystem
  if (fs.existsSync(attachment.path)) {
    fs.unlinkSync(attachment.path)
  }

  await attachment.deleteOne()

  // Emit socket event
  if (attachment.ticket) {
    io.to(req.user).emit(attachment.ticket, 'attachment:deleted', {
      attachmentId: req.id,
      deletedBy: req.user,
    })
  }
})
