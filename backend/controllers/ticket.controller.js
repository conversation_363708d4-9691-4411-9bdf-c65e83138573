const Ticket = require('../models/ticket')
const Message = require('../models/message')
const Category = require('../models/category')
const User = require('../models/user')
const ErrorResponse = require('../utils/errorResponse')
const asyncHandler = require('../middleware/async')
const { Socket, io } = require('../utils/socket')

const {
  sendTicketCreatedNotification,
  sendTicketAssignedNotification,
  sendTicketStatusUpdateNotification,
  sendTicketResolvedNotification,
  sendAgentAssignmentNotification,
} = require('../utils/emailNotifications')

// @desc    Get all tickets
// @route   GET /api/v1/tickets
// @access  Private
exports.getTickets = asyncHandler(async (req, res, next) => {
  // Get user from either req.user (HTTP) or directly from socket auth
  const user = req.user
  const userId = user?.id || user
  const userRole = user?.role || 'user'

  let query = {}

  // Filter by user role
  if (userRole === 'user') {
    query.user = userId
  } else if (userRole === 'agent') {
    // Agents can see assigned tickets and unassigned tickets
    query.$or = [
      { assignedAgent: userId },
      { assignedAgent: null },
    ]
  }

  // Admins can see all tickets (no filter)

  const tickets = await Ticket.find(query)
    .populate('user', 'name email')
    .populate('assignedAgent', 'name email')
    .populate('category', 'name color')
    .populate('messageCount')
    .sort('-lastActivity')

  io.to(req.user).emit('ticketController:getTickets', {
    status: 'success',
    data: {
      count: tickets.length,
      tickets: tickets,
    },
  })
})

// @desc    Get single ticket
// @route   GET /api/v1/tickets/:id
// @access  Private
exports.getTicket = asyncHandler(async (req, res, next) => {
  // Get ticket ID from either req.params.id (HTTP) or req.params?.id (Socket)
  const ticketId = req.params?.id || req.id

  // Get user from either req.user (HTTP) or directly from socket auth
  const user = req.user
  const userId = user?.id || user
  const userRole = user?.role || 'user'

  const ticket = await Ticket.findById(ticketId)
    .populate('user', 'name email role')
    .populate('assignedAgent', 'name email role')
    .populate('category', 'name color icon')
    .populate({
      path: 'messages',
      populate: {
        path: 'sender',
        select: 'name email role',
      },
      options: { sort: { createdAt: 1 } },
    })

  if (!ticket) {
    io.to(req.user).emit('ticketController:getTicket', {
      status: 'error',
      data: {
        message: 'Ticket not found',
      },
    })
  }

  // Check permissions
  if (userRole === 'user' && ticket.user._id.toString() !== userId) {
    io.to(req.user).emit('ticketController:getTicket', {
      status: 'error',
      data: {
        message: 'Not authorized to access this ticket',
      },
    })
  }

  io.to(req.user).emit('ticketController:getTicket', {
    status: 'success',
    data: ticket,
  })
})

// @desc    Create new ticket
// @route   POST /api/v1/tickets
// @access  Private
exports.createTicket = asyncHandler(async (req, res, next) => {
  // Get ticket data from either req.body (HTTP) or req (Socket)
  const ticketData = req.body || req

  // Get user ID from either req.user.id (HTTP) or req.user (Socket)
  const userId = req.user?.id || req.user

  // Get category ID from ticket data
  const categoryId = ticketData.category

  // Get category for auto-assignment
  const category = await Category.findById(categoryId)
  if (!category) {
    io.to(req.user).emit('ticketController:createTicket', {
      status: 'error',
      data: {
        message: 'Category not found',
      },
    })
  }

  // Create ticket data object
  const newTicketData = {
    ...ticketData,
    user: userId,
  }

  // Auto-assign agent if category has one
  if (category.autoAssignAgent) {
    newTicketData.assignedAgent = category.autoAssignAgent
  }

  // Set estimated resolution time from category
  if (category.estimatedResolutionTime) {
    newTicketData.estimatedResolutionTime = category.estimatedResolutionTime
  }

  const ticket = await Ticket.create(newTicketData)

  // Populate the created ticket
  await ticket.populate('user', 'name email')
  await ticket.populate('assignedAgent', 'name email')
  await ticket.populate('category', 'name color icon estimatedResolutionTime')

  // Send email notification to user
  const userObj = await User.findById(userId)

  sendTicketCreatedNotification(ticket, userObj, ticket.category)

  // Send email notification to assigned agent if any
  if (ticket.assignedAgent) {
    sendAgentAssignmentNotification(ticket, ticket.assignedAgent, userObj, ticket.category)
  }

  // Emit socket event for new ticket
  io.to(req.user).emit('ticket:created', {
    ticket,
    user: userObj,
  })

  // Emit to assigned agent if any
  if (ticket.assignedAgent) {
    io.to(req.user).emit(ticket.assignedAgent._id, 'ticket:assigned-to-you', {
      ticket,
      user: userObj,
    })
  }

  io.to(req.user).emit('ticketController:createTicket', {
    status: 'success',
    data: ticket,
  })
})

// @desc    Update ticket
// @route   PUT /api/v1/tickets/:id
// @access  Private
exports.updateTicket = asyncHandler(async (req, res, next) => {
  // Get ticket ID from either req.params.id (HTTP) or req.params?.id (Socket)
  const ticketId = req.params?.id || req.id

  // Get update data from either req.body (HTTP) or req (Socket)
  const updateData = req.body || req

  // Get user from either req.user (HTTP) or directly from socket auth
  const user = req.user
  const userId = user?.id || user
  const userRole = user?.role || 'user'

  let ticket = await Ticket.findById(ticketId)

  if (!ticket) {
    io.to(req.user).emit('ticketController:updateTicket', {
      status: 'error',
      data: {
        message: 'Ticket not found',
      },
    })
  }

  // Check permissions
  if (userRole === 'user' && ticket.user.toString() !== userId) {
    io.to(req.user).emit('ticketController:updateTicket', {
      status: 'error',
      data: {
        message: 'Not authorized to update this ticket',
      },
    })
  }

  // Store previous status for email notification
  const previousStatus = ticket.status

  // Users can only update certain fields
  let finalUpdateData = { ...updateData }
  if (userRole === 'user') {
    const allowedFields = ['title', 'description', 'priority']

    finalUpdateData = {}

    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        finalUpdateData[field] = updateData[field]
      }
    })
  }

  ticket = await Ticket.findByIdAndUpdate(ticketId, finalUpdateData, {
    new: true,
    runValidators: true,
  }).populate('user', 'name email')
    .populate('assignedAgent', 'name email')
    .populate('category', 'name color')

  // Send email notifications for status changes
  if (finalUpdateData.status && finalUpdateData.status !== previousStatus) {
    sendTicketStatusUpdateNotification(ticket, ticket.user, previousStatus, user)

    // Send resolved notification if ticket is resolved
    if (finalUpdateData.status === 'resolved') {
      sendTicketResolvedNotification(ticket, ticket.user, user)
    }
  }

  // Emit socket event for ticket update
  Socket.emitToTicket(ticketId, 'ticket:updated', {
    ticket,
    updatedBy: user,
  })

  io.to(req.user).emit('ticketController:updateTicket', {
    status: 'success',
    data: ticket,
  })
})

// @desc    Delete ticket
// @route   DELETE /api/v1/tickets/:id
// @access  Private (Admin only)
exports.deleteTicket = asyncHandler(async (req, res, next) => {
  // Get ticket ID from either req.params.id (HTTP) or req.params?.id (Socket)
  const ticketId = req.params?.id || req.id

  // Get user from either req.user (HTTP) or directly from socket auth
  const user = req.user
  const userRole = user?.role || 'user'

  const ticket = await Ticket.findById(ticketId)

  if (!ticket) {
    io.to(req.user).emit('ticketController:deleteTicket', {
      status: 'error',
      data: {
        message: 'Ticket not found',
      },
    })
  }

  // Only admins can delete tickets
  if (userRole !== 'admin') {
    io.to(req.user).emit('ticketController:deleteTicket', {
      status: 'error',
      data: {
        message: 'Not authorized to delete tickets',
      },
    })
  }

  await ticket.deleteOne()

  // Delete associated messages
  await Message.deleteMany({ ticket: ticketId })

  // Emit socket event for ticket deletion
  io.to(req.user).emit('ticket:deleted', {
    ticketId: ticketId,
    deletedBy: user,
  })

  io.to(req.user).emit('ticketController:deleteTicket', {
    status: 'success',
    data: {
      ticketId: ticketId,
      message: 'Ticket deleted successfully',
    },
  })
})

// @desc    Assign ticket to agent
// @route   PUT /api/v1/tickets/:id/assign
// @access  Private (Agent/Admin)
exports.assignTicket = asyncHandler(async (req, res, next) => {
  console.log('=== assignTicket CONTROLLER function called ===')
  console.log('req.params:', req.params)
  console.log('req.body:', req.body)
  console.log('req.user:', req.user)

  // Get ticket ID from either req.params.id (HTTP) or req.params?.id (Socket)
  const ticketId = req.params?.id || req.id
  console.log('ticketId:', ticketId)

  // Get agent ID from either req.body.agentId (HTTP) or req.agentId (Socket)
  const agentId = req.body?.agentId || req.agentId
  console.log('agentId:', agentId)

  // Get user from either req.user (HTTP) or directly from socket auth
  const user = req.user
  const userId = user?.id || user
  const userRole = user?.role || 'user'
  console.log('userId:', userId, 'userRole:', userRole)

  const ticket = await Ticket.findById(ticketId)

  if (!ticket) {
    io.to(req.user).emit('ticketController:assignTicket', {
      status: 'error',
      data: {
        message: 'Ticket not found',
      },
    })
  }

  // Only agents and admins can assign tickets
  if (!['agent', 'admin'].includes(userRole)) {
    io.to(req.user).emit('ticketController:assignTicket', {
      status: 'error',
      data: {
        message: 'Not authorized to assign tickets',
      },
    })
  }

  const updatedTicket = await Ticket.findByIdAndUpdate(
    ticketId,
    {
      assignedAgent: agentId,
      status: agentId ? 'in-progress' : 'open',
    },
    { new: true, runValidators: true },
  ).populate('user', 'name email')
    .populate('assignedAgent', 'name email')
    .populate('category', 'name color estimatedResolutionTime')

  // Send email notifications
  if (agentId) {
    // Notify customer about assignment
    sendTicketAssignedNotification(updatedTicket, updatedTicket.user, updatedTicket.assignedAgent)

    // Notify agent about assignment
    sendAgentAssignmentNotification(updatedTicket, updatedTicket.assignedAgent, updatedTicket.user, updatedTicket.category)
  }

  // Create system message
  console.log('Creating system message for ticket assignment')
  console.log('agentId:', agentId)
  console.log('updatedTicket.assignedAgent:', updatedTicket.assignedAgent)

  const messageContent = agentId
    ? `Ticket assigned to ${updatedTicket.assignedAgent?.name || 'Unknown Agent'}`
    : 'Ticket unassigned'

  console.log('Message content:', messageContent)

  console.log('=== ABOUT TO CREATE MESSAGE IN assignTicket CONTROLLER ===')
  const systemMessageData = {
    ticket: ticketId,
    sender: userId,
    content: messageContent,
    messageType: 'system',
  }
  console.log('systemMessageData:', JSON.stringify(systemMessageData, null, 2))
  console.log('Stack trace:')
  console.trace()

  try {
    await Message.create(systemMessageData)
    console.log('=== SYSTEM MESSAGE CREATED SUCCESSFULLY ===')
  } catch (error) {
    console.log('=== SYSTEM MESSAGE CREATION ERROR IN assignTicket CONTROLLER ===')
    console.log('System message creation error:', error)
    throw error
  }

  // Emit socket event
  io.to(req.user).emit(ticketId, 'ticket:assigned', {
    ticket: updatedTicket,
    assignedBy: user,
  })

  // Notify assigned agent
  if (agentId) {
    io.to(req.user).emit(agentId, 'ticket:assigned-to-you', {
      ticket: updatedTicket,
      assignedBy: user,
    })
  }

  io.to(req.user).emit('ticketController:assignTicket', {
    status: 'success',
    data: updatedTicket,
  })
})
