import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { socket } from'@socket/socket'

export const useUserStore = defineStore('users', () => {
  const users = ref(null)
  const singleUser = ref(null)
  const loading = ref(false)
  const error = ref(null)

  function $reset() {
    users.value = null
    singleUser.value = null
  }

  function getAllUsers(data) {
    console.log('Store: getAllUsers called with:', data)
    socket.emit('userController:getUsers', data)
  }

  function getUser(data) {
    console.log('Store: getUser called with:', data)
    socket.emit('userController:getUser', data)
  }

  function createUser(data) {
    console.log('Store: createUser called with:', data)
    socket.emit('userController:createUser', data)
  }

  function updateUser(data) {
    console.log('Store: updateUser called with:', data)
    socket.emit('userController:updateUser', data)
  }

  function deleteUser(data) {
    console.log('Store: deleteUser called with:', data)
    socket.emit('userController:deleteUser', data)
  }

  return {
    users,
    singleUser,
    loading,
    error,
    $reset,
    getAllUsers,
    getUser,
    createUser,
    updateUser,
    deleteUser,
  }
}, {
  persistedState: {
    persist: true,
    paths: ['tickets', 'currentTicket'],
  },
})
