<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useSocketStore } from '@stores/auth'
import { useUserStore } from '@stores/users'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'

definePage({
  meta: {
    layout: 'default',
  },
})

const authStore = useSocketStore()
const userStore = useUserStore()

const { user } = storeToRefs(authStore)
const { users } = storeToRefs(userStore)

socket.on('userController:getUsers', data => {
  users.value = data.data
})

socket.on('userController:createUser', response => {
  if (response.status === 'success') {
    toast.success('👤 User created successfully!')
    showCreateDialog.value = false
    resetForm()
    loadUsers() // Refresh user list
  } else {
    toast.error(`❌ Failed to create user: ${response.data?.message || 'Unknown error'}`)
  }
})

// Listen for user update responses
socket.on('userController:updateUser', response => {
  if (response.status === 'success') {
    toast.success('✏️ User updated successfully!')
    showEditDialog.value = false
    editingUser.value = null
    resetForm()
    loadUsers() // Refresh user list
  } else {
    toast.error(`❌ Failed to update user: ${response.data?.message || 'Unknown error'}`)
  }
})

// Listen for user deletion responses
socket.on('userController:deleteUser', response => {
  if (response.status === 'success') {
    toast.success('🗑️ User deleted successfully!')
    loadUsers() // Refresh user list
  } else {
    toast.error(`❌ Failed to delete user: ${response.data?.message || 'Unknown error'}`)
  }
})

onMounted(() => {
  if (!canManageUsers.value) {
    toast.error('You do not have permission to manage users')

    return
  }

  if (!users.value) {
    userStore.getAllUsers({
      user: user.value._id,
    })
  }
})

const loading = ref(false)
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const editingUser = ref(null)

const form = ref({
  name: '',
  email: '',
  role: 'user',
  password: '',
})

const canManageUsers = computed(() => {
  return user.value && user.value.role === 'admin'
})

const roleOptions = [
  { title: 'User', value: 'user' },
  { title: 'Agent', value: 'agent' },
  { title: 'Admin', value: 'admin' },
]

function loadUsers() {
  loading.value = true

  // Simulate API call
  setTimeout(() => {
    users.value = mockUsers
    loading.value = false
  }, 500)
}

function resetForm() {
  form.value = {
    name: '',
    email: '',
    role: 'user',
    password: '',
  }
}

function handleCreate() {
  resetForm()
  showCreateDialog.value = true
}

function handleCreateSubmit() {
  // Emit socket event for user creation
  socket.emit('userController:createUser', {
    body: form.value,
  })
}

function handleEdit(userToEdit) {
  editingUser.value = userToEdit
  form.value = {
    name: userToEdit.name,
    email: userToEdit.email,
    role: userToEdit.role,
    password: '',
  }
  showEditDialog.value = true
}

function handleEditSubmit() {
  // Emit socket event for user update
  if (editingUser.value) {
    socket.emit('userController:updateUser', {
      params: { id: editingUser.value._id },
      body: form.value,
    })
  }
}

function handleDelete(userToDelete) {
  if (confirm(`Are you sure you want to delete user "${userToDelete.name}"?`)) {
    socket.emit('userController:deleteUser', {
      params: { id: userToDelete._id },
    })
  }
}

function getRoleColor(role) {
  const colors = {
    'user': 'primary',
    'agent': 'info',
    'admin': 'error',
  }
  
  return colors[role] || 'primary'
}

function formatDate(date) {
  if (!date) return 'Never'
  
  return new Date(date).toLocaleDateString()
}
</script>

<template>
  <div>
    <VRow>
      <VCol cols="12">
        <div class="d-flex justify-space-between align-center mb-6">
          <h1 class="text-h4">
            Manage Users
          </h1>
          <VBtn
            v-if="canManageUsers"
            color="primary"
            prepend-icon="tabler-plus"
            @click="handleCreate"
          >
            Create User
          </VBtn>
        </div>
      </VCol>
    </VRow>

    <VRow v-if="!canManageUsers">
      <VCol cols="12">
        <VAlert
          type="warning"
          variant="tonal"
        >
          You do not have permission to manage users.
        </VAlert>
      </VCol>
    </VRow>

    <VRow v-else>
      <VCol cols="12">
        <!-- Loading State -->
        <div
          v-if="!users"
          class="text-center py-8"
        >
          <VProgressCircular
            indeterminate
            color="primary"
          />
          <p class="mt-4">
            Loading users...
          </p>
        </div>

        <!-- Users Table -->
        <VCard v-else>
          <VCardTitle>Users</VCardTitle>
          <VCardText>
            <VDataTable
              :items="users"
              :headers="[
                { title: 'Name', key: 'name' },
                { title: 'Email', key: 'email' },
                { title: 'Role', key: 'role' },
                { title: 'Created', key: 'createdAt' },
                { title: 'Last Login', key: 'lastLogin' },
                { title: 'Actions', key: 'actions', sortable: false }
              ]"
              item-value="_id"
            >
              <template #item.role="{ item }">
                <VChip
                  :color="getRoleColor(item.role)"
                  size="small"
                  variant="tonal"
                >
                  {{ item.role }}
                </VChip>
              </template>

              <template #item.createdAt="{ item }">
                {{ formatDate(item.createdAt) }}
              </template>

              <template #item.lastLogin="{ item }">
                {{ formatDate(item.lastLogin) }}
              </template>

              <template #item.actions="{ item }">
                <VBtn
                  icon="tabler-edit"
                  size="small"
                  variant="text"
                  @click="handleEdit(item)"
                />
                <VBtn
                  v-if="item._id !== user._id"
                  icon="tabler-trash"
                  size="small"
                  variant="text"
                  color="error"
                  @click="handleDelete(item)"
                />
              </template>
            </VDataTable>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- Create Dialog -->
    <VDialog
      v-model="showCreateDialog"
      max-width="600px"
      persistent
    >
      <VCard>
        <VCardTitle>Create User</VCardTitle>
        <VCardText>
          <VForm>
            <VRow>
              <VCol cols="12">
                <VTextField
                  v-model="form.name"
                  label="Name"
                  variant="outlined"
                  required
                />
              </VCol>

              <VCol cols="12">
                <VTextField
                  v-model="form.email"
                  label="Email"
                  type="email"
                  variant="outlined"
                  required
                />
              </VCol>

              <VCol cols="12">
                <VTextField
                  v-model="form.password"
                  label="Password"
                  type="password"
                  variant="outlined"
                  required
                />
              </VCol>

              <VCol cols="12">
                <VSelect
                  v-model="form.role"
                  :items="roleOptions"
                  label="Role"
                  variant="outlined"
                  required
                />
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn
            variant="outlined"
            @click="showCreateDialog = false"
          >
            Cancel
          </VBtn>
          <VBtn
            color="primary"
            @click="handleCreateSubmit"
          >
            Create
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>

    <!-- Edit Dialog -->
    <VDialog
      v-model="showEditDialog"
      max-width="600px"
      persistent
    >
      <VCard>
        <VCardTitle>Edit User</VCardTitle>
        <VCardText>
          <VForm>
            <VRow>
              <VCol cols="12">
                <VTextField
                  v-model="form.name"
                  label="Name"
                  variant="outlined"
                  required
                />
              </VCol>

              <VCol cols="12">
                <VTextField
                  v-model="form.email"
                  label="Email"
                  type="email"
                  variant="outlined"
                  required
                />
              </VCol>

              <VCol cols="12">
                <VTextField
                  v-model="form.password"
                  label="New Password (leave blank to keep current)"
                  type="password"
                  variant="outlined"
                />
              </VCol>

              <VCol cols="12">
                <VSelect
                  v-model="form.role"
                  :items="roleOptions"
                  label="Role"
                  variant="outlined"
                  required
                />
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn
            variant="outlined"
            @click="showEditDialog = false"
          >
            Cancel
          </VBtn>
          <VBtn
            color="primary"
            @click="handleEditSubmit"
          >
            Update
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </div>
</template>
